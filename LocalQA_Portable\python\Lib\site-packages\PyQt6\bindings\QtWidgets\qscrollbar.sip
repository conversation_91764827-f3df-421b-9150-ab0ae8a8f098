// qscrollbar.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QScrollBar : public QAbstractSlider
{
%TypeHeaderCode
#include <qscrollbar.h>
%End

public:
    explicit QScrollBar(QWidget *parent /TransferThis/ = 0);
    QScrollBar(Qt::Orientation orientation, QWidget *parent /TransferThis/ = 0);
    virtual ~QScrollBar();
    virtual QSize sizeHint() const;
    virtual bool event(QEvent *event);

protected:
    virtual void initStyleOption(QStyleOptionSlider *option) const;
    virtual void paintEvent(QPaintEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual void sliderChange(QAbstractSlider::SliderChange change);
};
