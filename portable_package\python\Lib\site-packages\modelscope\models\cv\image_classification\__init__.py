# Copyright (c) Alibaba, Inc. and its affiliates.
from typing import TYPE_CHECKING

from modelscope.utils.import_utils import LazyImportModule

if TYPE_CHECKING:
    from .mmcls_model import ClassificationModel
    from .resnet50_cc import ContentCheckBackbone

else:
    _import_structure = {
        'mmcls_model': ['ClassificationModel'],
        'resnet50_cc': ['ContentCheckBackbone'],
    }

    import sys

    sys.modules[__name__] = LazyImportModule(
        __name__,
        globals()['__file__'],
        _import_structure,
        module_spec=__spec__,
        extra_objects={},
    )
