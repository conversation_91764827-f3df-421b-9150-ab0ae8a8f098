#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
便携版功能测试脚本
测试AI模型加载、问答功能和制度检索功能
"""

import sys
import os
import time

# 添加app目录到Python路径
sys.path.insert(0, 'app')

def test_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    try:
        from src.utils.helpers import resource_path
        from src.utils.config import get_config
        from src.core.ai_model import AIModel
        from src.core.search_system import PolicySearchSystem
        print("✅ 基础导入成功")
        return True
    except Exception as e:
        print(f"❌ 基础导入失败: {e}")
        return False

def test_resource_path():
    """测试资源路径解析"""
    print("\n🔍 测试资源路径解析...")
    try:
        from src.utils.helpers import resource_path
        
        # 测试模型路径
        model_path = resource_path('models/qwen-1.5-1.8b-chat')
        print(f"模型路径: {model_path}")
        print(f"模型存在: {os.path.exists(model_path)}")
        
        # 测试配置文件路径
        config_path = resource_path('config.yaml')
        print(f"配置路径: {config_path}")
        print(f"配置存在: {os.path.exists(config_path)}")
        
        print("✅ 资源路径解析成功")
        return True
    except Exception as e:
        print(f"❌ 资源路径解析失败: {e}")
        return False

def test_ai_model():
    """测试AI模型加载和问答"""
    print("\n🔍 测试AI模型...")
    try:
        from src.core.ai_model import AIModel
        
        # 初始化AI模型
        ai = AIModel()
        print("AI模型初始化成功")
        
        # 获取模型信息
        info = ai.get_model_info()
        print(f"模型信息: {info['model_name']}")
        print(f"模型路径: {info['model_path']}")
        print(f"模型存在: {info['model_exists']}")
        
        if not info['model_exists']:
            print("❌ 模型文件不存在")
            return False
        
        # 加载模型
        print("正在加载AI模型...")
        start_time = time.time()
        success = ai.load_model()
        load_time = time.time() - start_time
        
        if not success:
            print("❌ 模型加载失败")
            return False
        
        print(f"✅ 模型加载成功，耗时: {load_time:.2f}秒")
        
        # 测试问答
        print("测试AI问答...")
        response = ai.generate_response("你好，请简单介绍一下自己")
        print(f"AI回复: {response[:100]}...")
        
        print("✅ AI模型测试成功")
        return True
        
    except Exception as e:
        print(f"❌ AI模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_system():
    """测试搜索系统"""
    print("\n🔍 测试搜索系统...")
    try:
        from src.core.search_system import PolicySearchSystem

        # 初始化搜索系统
        search_system = PolicySearchSystem()
        print("搜索系统初始化成功")
        
        # 测试向量搜索（即使没有文档也应该能正常运行）
        results = search_system.vector_search("测试查询", top_k=5)
        print(f"向量搜索结果数量: {len(results)}")
        
        # 测试全文搜索
        results = search_system.text_search("测试查询", top_k=5)
        print(f"全文搜索结果数量: {len(results)}")
        
        print("✅ 搜索系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 搜索系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始便携版功能测试")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_imports),
        ("资源路径", test_resource_path),
        ("AI模型", test_ai_model),
        ("搜索系统", test_search_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！便携版功能正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
