import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qcapsulegeometry_p.h"
        name: "CapsuleGeometry"
        accessSemantics: "reference"
        prototype: "QQuick3DGeometry"
        exports: [
            "QtQuick3D.Physics.Helpers/CapsuleGeometry 6.0",
            "QtQuick3D.Physics.Helpers/CapsuleGeometry 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1543]
        Property {
            name: "enableNormals"
            type: "bool"
            read: "enableNormals"
            write: "setEnableNormals"
            notify: "enableNormalsChanged"
            index: 0
        }
        Property {
            name: "enableUV"
            type: "bool"
            read: "enableUV"
            write: "setEnableUV"
            notify: "enableUVChanged"
            index: 1
        }
        Property {
            name: "longitudes"
            type: "int"
            read: "longitudes"
            write: "setLongitudes"
            notify: "longitudesChanged"
            index: 2
        }
        Property {
            name: "latitudes"
            type: "int"
            read: "latitudes"
            write: "setLatitudes"
            notify: "latitudesChanged"
            index: 3
        }
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 4
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 5
        }
        Property {
            name: "diameter"
            type: "float"
            read: "diameter"
            write: "setDiameter"
            notify: "diameterChanged"
            index: 6
        }
        Signal { name: "enableNormalsChanged" }
        Signal { name: "enableUVChanged" }
        Signal { name: "longitudesChanged" }
        Signal { name: "latitudesChanged" }
        Signal { name: "ringsChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "diameterChanged" }
    }
}
