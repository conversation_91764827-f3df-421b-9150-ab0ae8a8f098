2025-07-01 13:40:02 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:40:02 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:40:02 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:40:02 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 13:40:02 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 13:40:02 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | memory_available_gb: 19.86
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 13:40:02 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 13:40:02 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 13:40:02 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 13:40:02 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 13:40:02 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 13:40:02 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 13:40:02 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 13:40:02 | INFO     | __main__:main:93 | 检查Python依赖...
2025-07-01 13:40:02 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 13:40:02 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: False
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.2.2+cpu)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.24)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.26)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.2)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 13:40:16 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 13:40:16 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 13:40:16 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 13:40:16 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 13:40:16 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\portable_package\app\data
2025-07-01 13:40:16 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\portable_package\app\docs
2025-07-01 13:40:16 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 13:40:21 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 13:40:21 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 13:40:22 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 13:40:22 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 新对话
2025-07-01 13:40:22 | INFO     | src.core.session_manager:_load_or_create_default_session:96 | 创建默认会话
2025-07-01 13:40:22 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 新对话, ID: ed82834c-d881-4334-be59-6f5367f0f937, 数据: session_ed82834c-d881-4334-be59-6f5367f0f937
2025-07-01 13:40:22 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 新对话
2025-07-01 13:40:22 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 新对话
2025-07-01 13:40:22 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 13:40:22 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 13:40:22 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 13:40:22 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 13:40:22 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 13:40:22 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 13:40:22 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:40:22 | INFO     | src.core.vector_store:_initialize_client:116 | 创建新集合: policy_documents
2025-07-01 13:40:22 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\portable_package\app\models\shibing624\text2vec-base-chinese
2025-07-01 13:40:23 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cpu
2025-07-01 13:40:23 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 13:40:23 | INFO     | src.core.text_index:_initialize_index:126 | 创建新索引: D:\LocalQA\portable_package\app\data\whoosh_index
2025-07-01 13:40:23 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 13:40:23 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:40:23 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 13:40:23 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 13:40:23 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 13:40:23 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 13:40:23 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 13:40:23 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 13:43:39 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 13:43:39 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 13:43:39 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 13:44:25 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 0 个结果
2025-07-01 13:44:25 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 13:44:25 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '业务招待费', 返回 0 个结果
2025-07-01 13:44:25 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 13:44:25 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: ➕ 新建对话, 类型: new_chat
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_on_nav_item_clicked:472 | 创建新会话
2025-07-01 13:44:28 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 对话 07-01 13:44
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:44, ID: b99401fe-d69c-4742-afa5-b869bbe267da, 数据: session_b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:44
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:44
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:44, ID: b99401fe-d69c-4742-afa5-b869bbe267da, 数据: session_b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:44
2025-07-01 13:44:28 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:44
2025-07-01 13:44:30 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 13:44:30 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 0 个结果
2025-07-01 13:44:30 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 13:44:32 | INFO     | src.core.device_manager:_apply_cpu_optimization:107 | 应用CPU性能优化...
2025-07-01 13:44:32 | INFO     | src.core.device_manager:_apply_cpu_optimization:134 | PyTorch线程数设置为: 8
2025-07-01 13:44:32 | INFO     | src.core.device_manager:_apply_cpu_optimization:147 | Intel MKL优化已启用
2025-07-01 13:44:32 | INFO     | src.core.device_manager:_apply_cpu_optimization:152 | Intel MKL-DNN优化已启用
2025-07-01 13:44:32 | INFO     | src.core.device_manager:_apply_cpu_optimization:158 | CPU优化完成: 使用8个线程
2025-07-01 13:44:32 | INFO     | src.core.device_manager:switch_device:98 | 设备已切换到: cpu
2025-07-01 13:44:32 | INFO     | src.core.ai_model:load_model:142 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 13:44:32 | INFO     | src.core.ai_model:load_model:157 | 当前内存使用: 1128.4MB (3.5%)
2025-07-01 13:44:32 | INFO     | src.core.ai_model:load_model:171 | 加载tokenizer...
2025-07-01 13:44:32 | INFO     | src.core.ai_model:load_model:182 | 加载模型...
2025-07-01 13:44:35 | INFO     | src.core.ai_model:load_model:221 | 模型加载成功！
2025-07-01 13:44:35 | INFO     | src.core.ai_model:load_model:222 | 加载时间: 2.89秒
2025-07-01 13:44:35 | INFO     | src.core.ai_model:load_model:223 | 内存增加: 7830.4MB
2025-07-01 13:44:35 | INFO     | src.core.ai_model:load_model:224 | 设备: cpu
2025-07-01 13:44:35 | INFO     | src.core.ai_model:_warmup_model:252 | 开始模型预热...
2025-07-01 13:44:36 | INFO     | src.core.ai_model:_warmup_model:294 | 模型预热完成
2025-07-01 13:44:39 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 13:44:39 | INFO     | src.core.search_system:vector_search:221 | 从缓存返回向量搜索结果: 业务招待费
2025-07-01 13:44:40 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 13:44:40 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 13:44:40 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 13:44:42 | INFO     | src.core.search_system:hybrid_search:316 | 从缓存返回混合搜索结果: 业务招待费
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 对话 07-01 13:44, 类型: session_b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_b99401fe-d69c-4742-afa5-b869bbe267da, 提取的会话ID: b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:43 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 业务招待费
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费, ID: b99401fe-d69c-4742-afa5-b869bbe267da, 数据: session_b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 业务招待费
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:44:43 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 13:45:54 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 对话 07-01 13:45
2025-07-01 13:45:54 | INFO     | src.core.session_manager:delete_session:193 | 删除会话: b99401fe-d69c-4742-afa5-b869bbe267da
2025-07-01 13:45:54 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:45:54 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 13:45:54 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 13:45:54 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 对话 07-01 13:45, 类型: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_b59306c5-3adc-4618-8109-e979d2790112, 提取的会话ID: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:45:57 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 对话 07-01 13:45
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 对话 07-01 13:45
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:45:57 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 13:45:59 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 13:45:59 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 13:45:59 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 对话 07-01 13:45, 类型: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_b59306c5-3adc-4618-8109-e979d2790112, 提取的会话ID: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:46:01 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 对话 07-01 13:45
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 对话 07-01 13:45
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:46:01 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 13:46:03 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 13:46:04 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
2025-07-01 13:52:09 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:52:09 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:52:09 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:52:09 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:52:53 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:52:53 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:52:53 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:52:53 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:52:53 | INFO     | src.core.ai_model:load_model:142 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 13:52:53 | INFO     | src.core.ai_model:load_model:157 | 当前内存使用: 170.9MB (0.5%)
2025-07-01 13:52:53 | INFO     | src.core.ai_model:load_model:171 | 加载tokenizer...
2025-07-01 13:52:53 | INFO     | src.core.ai_model:load_model:182 | 加载模型...
2025-07-01 13:52:54 | INFO     | src.core.ai_model:load_model:221 | 模型加载成功！
2025-07-01 13:52:54 | INFO     | src.core.ai_model:load_model:222 | 加载时间: 1.38秒
2025-07-01 13:52:54 | INFO     | src.core.ai_model:load_model:223 | 内存增加: 7833.7MB
2025-07-01 13:52:54 | INFO     | src.core.ai_model:load_model:224 | 设备: cpu
2025-07-01 13:52:54 | INFO     | src.core.ai_model:_warmup_model:252 | 开始模型预热...
2025-07-01 13:52:55 | INFO     | src.core.ai_model:_warmup_model:294 | 模型预热完成
2025-07-01 13:53:18 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:53:18 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:53:18 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:53:18 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:53:18 | INFO     | src.core.ai_model:load_model:142 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 13:53:18 | INFO     | src.core.ai_model:load_model:157 | 当前内存使用: 171.2MB (0.5%)
2025-07-01 13:53:18 | INFO     | src.core.ai_model:load_model:171 | 加载tokenizer...
2025-07-01 13:53:19 | INFO     | src.core.ai_model:load_model:182 | 加载模型...
2025-07-01 13:53:20 | INFO     | src.core.ai_model:load_model:221 | 模型加载成功！
2025-07-01 13:53:20 | INFO     | src.core.ai_model:load_model:222 | 加载时间: 1.22秒
2025-07-01 13:53:20 | INFO     | src.core.ai_model:load_model:223 | 内存增加: 7833.4MB
2025-07-01 13:53:20 | INFO     | src.core.ai_model:load_model:224 | 设备: cpu
2025-07-01 13:53:20 | INFO     | src.core.ai_model:_warmup_model:252 | 开始模型预热...
2025-07-01 13:53:21 | INFO     | src.core.ai_model:_warmup_model:294 | 模型预热完成
2025-07-01 13:53:21 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 13:53:21 | INFO     | src.core.ai_model:generate_response:427 | 开始生成回复...
2025-07-01 13:53:36 | INFO     | src.core.ai_model:generate_response:437 | 生成完成，耗时: 15.50秒
2025-07-01 13:53:46 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:53:46 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:53:46 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:53:46 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 13:53:46 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 13:53:46 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | memory_available_gb: 19.92
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 13:53:46 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 13:53:46 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 13:53:46 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 13:53:46 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 13:53:46 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 13:53:46 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 13:53:46 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 13:53:46 | INFO     | __main__:main:93 | 检查Python依赖...
2025-07-01 13:53:46 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 13:53:46 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: False
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.2.2+cpu)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.24)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.26)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.2)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 13:53:47 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 13:53:47 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 13:53:47 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 13:53:47 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 13:53:47 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 13:53:49 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 13:53:49 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 13:53:50 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 13:53:50 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 对话 07-01 13:45
2025-07-01 13:53:50 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 13:53:50 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 13:53:50 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 13:53:50 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 13:53:50 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 13:53:50 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 13:53:50 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 13:53:50 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 13:53:50 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 13:53:50 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:53:50 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 13:53:50 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\portable_package\app\models\shibing624\text2vec-base-chinese
2025-07-01 13:53:50 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cpu
2025-07-01 13:53:50 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 13:53:50 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\portable_package\app\data\whoosh_index
2025-07-01 13:53:50 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 13:53:50 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:53:50 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 13:53:50 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 13:53:50 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 13:53:50 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 13:53:50 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 13:53:50 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 13:55:28 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:55:28 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:55:28 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:55:31 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:55:31 | INFO     | src.core.ai_model:load_model:142 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 13:55:31 | INFO     | src.core.ai_model:load_model:157 | 当前内存使用: 484.2MB (1.5%)
2025-07-01 13:55:31 | INFO     | src.core.ai_model:load_model:171 | 加载tokenizer...
2025-07-01 13:55:31 | INFO     | src.core.ai_model:load_model:182 | 加载模型...
2025-07-01 13:55:32 | INFO     | src.core.ai_model:load_model:221 | 模型加载成功！
2025-07-01 13:55:32 | INFO     | src.core.ai_model:load_model:222 | 加载时间: 1.21秒
2025-07-01 13:55:32 | INFO     | src.core.ai_model:load_model:223 | 内存增加: 7825.4MB
2025-07-01 13:55:32 | INFO     | src.core.ai_model:load_model:224 | 设备: cpu
2025-07-01 13:55:32 | INFO     | src.core.ai_model:_warmup_model:252 | 开始模型预热...
2025-07-01 13:55:33 | INFO     | src.core.ai_model:_warmup_model:294 | 模型预热完成
2025-07-01 13:55:33 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 13:55:33 | INFO     | src.core.ai_model:generate_response:427 | 开始生成回复...
2025-07-01 13:55:49 | INFO     | src.core.ai_model:generate_response:437 | 生成完成，耗时: 15.69秒
2025-07-01 13:56:40 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:56:40 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 13:56:40 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 13:56:43 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:56:43 | INFO     | src.core.ai_model:load_model:142 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 13:56:43 | INFO     | src.core.ai_model:load_model:157 | 当前内存使用: 484.0MB (1.5%)
2025-07-01 13:56:43 | INFO     | src.core.ai_model:load_model:171 | 加载tokenizer...
2025-07-01 13:56:43 | INFO     | src.core.ai_model:load_model:182 | 加载模型...
2025-07-01 13:56:45 | INFO     | src.core.ai_model:load_model:221 | 模型加载成功！
2025-07-01 13:56:45 | INFO     | src.core.ai_model:load_model:222 | 加载时间: 1.17秒
2025-07-01 13:56:45 | INFO     | src.core.ai_model:load_model:223 | 内存增加: 7825.3MB
2025-07-01 13:56:45 | INFO     | src.core.ai_model:load_model:224 | 设备: cpu
2025-07-01 13:56:45 | INFO     | src.core.ai_model:_warmup_model:252 | 开始模型预热...
2025-07-01 13:56:45 | INFO     | src.core.ai_model:_warmup_model:294 | 模型预热完成
2025-07-01 13:56:45 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 13:56:45 | INFO     | src.core.ai_model:generate_response:427 | 开始生成回复...
2025-07-01 13:57:01 | INFO     | src.core.ai_model:generate_response:437 | 生成完成，耗时: 15.28秒
2025-07-01 13:57:01 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 13:57:01 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:57:01 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 13:57:01 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\portable_package\app\models\shibing624\text2vec-base-chinese
2025-07-01 13:57:01 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cpu
2025-07-01 13:57:01 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 13:57:01 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\portable_package\app\data\whoosh_index
2025-07-01 13:57:01 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 13:57:01 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 13:57:01 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 13:57:01 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 13:57:01 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 0 个结果
2025-07-01 13:57:01 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 测试查询, 返回 0 个结果
2025-07-01 13:57:01 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '测试查询', 返回 0 个结果
2025-07-01 13:57:01 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 测试查询, 返回 0 个结果
2025-07-01 14:04:17 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 14:04:17 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 14:04:17 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 14:04:17 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 14:04:17 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 14:04:17 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | memory_available_gb: 20.09
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 14:04:17 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 14:04:17 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 14:04:17 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 14:04:17 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 14:04:17 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 14:04:17 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 14:04:17 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 14:04:17 | INFO     | __main__:main:93 | 检查Python依赖...
2025-07-01 14:04:17 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 14:04:17 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: False
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.2.2+cpu)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.24)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.26)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.2)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 14:04:18 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 14:04:18 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 14:04:18 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 14:04:18 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 14:04:18 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 14:04:20 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 14:04:20 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 14:04:21 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 14:04:21 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 对话 07-01 13:45
2025-07-01 14:04:21 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:04:21 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 14:04:21 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 14:04:21 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 14:04:21 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 14:04:21 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 14:04:21 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 14:04:21 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 14:04:21 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 14:04:21 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 14:04:21 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 14:04:21 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\portable_package\app\models\shibing624\text2vec-base-chinese
2025-07-01 14:04:21 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cpu
2025-07-01 14:04:21 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 14:04:21 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\portable_package\app\data\whoosh_index
2025-07-01 14:04:21 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 14:04:21 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 14:04:21 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 14:04:21 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 14:04:21 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 14:04:21 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 14:04:21 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 14:04:21 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 14:04:55 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 14:04:55 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 14:04:55 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 14:04:55 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 14:04:55 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 14:04:55 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | memory_available_gb: 19.33
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | gpu_available: False
2025-07-01 14:04:55 | INFO     | __main__:check_environment:58 | gpu_count: 0
2025-07-01 14:04:55 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 14:04:55 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 14:04:55 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 14:04:55 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 14:04:55 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 14:04:55 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 14:04:55 | INFO     | __main__:main:93 | 检查Python依赖...
2025-07-01 14:04:55 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 14:04:55 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: False
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.2.2+cpu)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.24)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.26)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.2)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 14:04:56 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 14:04:56 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 14:04:56 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 14:04:56 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 14:04:56 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 14:04:58 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 14:04:58 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 14:04:58 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 14:04:59 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 对话 07-01 13:45
2025-07-01 14:04:59 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:04:59 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 14:04:59 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 14:04:59 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 14:04:59 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 14:04:59 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 14:04:59 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 14:04:59 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 14:04:59 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 14:04:59 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 14:04:59 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 14:04:59 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\portable_package\app\models\shibing624\text2vec-base-chinese
2025-07-01 14:04:59 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cpu
2025-07-01 14:04:59 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 14:04:59 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\portable_package\app\data\whoosh_index
2025-07-01 14:04:59 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 14:04:59 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 14:04:59 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 14:04:59 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 14:04:59 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 14:04:59 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 14:04:59 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 14:04:59 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 14:17:37 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 14:17:37 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 14:17:37 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 14:17:42 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 0 个结果
2025-07-01 14:17:42 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 14:17:42 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '业务招待费', 返回 0 个结果
2025-07-01 14:17:42 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 14:17:42 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 对话 07-01 13:45, 类型: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_b59306c5-3adc-4618-8109-e979d2790112, 提取的会话ID: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:17:49 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 对话 07-01 13:45
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 13:45, ID: b59306c5-3adc-4618-8109-e979d2790112, 数据: session_b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 13:45
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 13:45
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 对话 07-01 13:45
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: b59306c5-3adc-4618-8109-e979d2790112
2025-07-01 14:17:49 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 14:17:52 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费
2025-07-01 14:17:52 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 0 个结果
2025-07-01 14:17:52 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 0 个结果
2025-07-01 14:17:59 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 14:18:00 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
2025-07-01 14:18:02 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 14:18:03 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
2025-07-01 15:02:24 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 15:02:24 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 15:02:24 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 15:02:24 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 15:02:24 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 15:02:24 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 15:02:24 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\portable_package\app\models\shibing624\text2vec-base-chinese
2025-07-01 15:02:24 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cpu
2025-07-01 15:02:24 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 15:02:24 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\portable_package\app\data\whoosh_index
2025-07-01 15:02:24 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 15:02:24 | INFO     | src.utils.helpers:get_optimal_device:119 | 使用CPU设备
2025-07-01 15:02:24 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 15:02:24 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 15:02:24 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 你好，请介绍一下自己
2025-07-01 15:02:24 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 0 个结果
2025-07-01 15:02:24 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 你好，请介绍一下自己, 返回 0 个结果
