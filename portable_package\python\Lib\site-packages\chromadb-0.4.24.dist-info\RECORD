../../Scripts/chroma.exe,sha256=VTjqn5vfyzhve3yIMjkTENd3zcpY4yXO19E2WvQQDZk,108394
chromadb-0.4.24.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
chromadb-0.4.24.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
chromadb-0.4.24.dist-info/METADATA,sha256=uS9OxhF8WAUDTa-uq8JXWjocvuuPjWSFCo7eJymLIMU,7338
chromadb-0.4.24.dist-info/RECORD,,
chromadb-0.4.24.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb-0.4.24.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
chromadb-0.4.24.dist-info/entry_points.txt,sha256=VlVPAsiw1li6DlT0MWFqr3qi_5dj_ZjttNYYoQ7vVmg,48
chromadb-0.4.24.dist-info/top_level.txt,sha256=gUwkTunXI0uoZki9FEiZLW9ML6FJEJ27MpgG3bIDnD4,9
chromadb/__init__.py,sha256=2z9x3mtqobdzVYGow2noykdQKUEaKqQq1176HisOkZc,9013
chromadb/__pycache__/__init__.cpython-310.pyc,,
chromadb/__pycache__/app.cpython-310.pyc,,
chromadb/__pycache__/config.cpython-310.pyc,,
chromadb/__pycache__/errors.cpython-310.pyc,,
chromadb/__pycache__/types.cpython-310.pyc,,
chromadb/api/__init__.py,sha256=OVAgJJEhFc66Rn_fzIq_Hs2iGLS_s5KU8mKEidD0r_M,18836
chromadb/api/__pycache__/__init__.cpython-310.pyc,,
chromadb/api/__pycache__/client.cpython-310.pyc,,
chromadb/api/__pycache__/fastapi.cpython-310.pyc,,
chromadb/api/__pycache__/segment.cpython-310.pyc,,
chromadb/api/__pycache__/types.cpython-310.pyc,,
chromadb/api/client.py,sha256=35xQOc0xhaehV2OqOf4t99Rlmj_rDe6ucuNRaKtCWiE,15345
chromadb/api/fastapi.py,sha256=wbNVc42ynU1IhlgOJ_jFjNjfr0CPLFnjjzvEVaQ8ZFM,21927
chromadb/api/models/Collection.py,sha256=3HUaQVYbA8odW_qazJOhD6siFfAY_sHak3S7OFSiWhQ,23247
chromadb/api/models/__pycache__/Collection.cpython-310.pyc,,
chromadb/api/segment.py,sha256=nmqiHyvbcS98wrWssTzQrKJeP_wkkzDUWyNoz6wDIcs,33608
chromadb/api/types.py,sha256=7mYQ0yPNxUt_5P-ai5WG11-WzRWaxMBT82IcZ9wO0mM,18471
chromadb/app.py,sha256=GzDY70YzAUqrWgqG9kxD4ItB4OkynjiGIEGchk0hhNA,168
chromadb/auth/__init__.py,sha256=-Vlmb6LB6f_hx03dYCrgX-uvTjkVBv_xTs8PReexYkw,11668
chromadb/auth/__pycache__/__init__.cpython-310.pyc,,
chromadb/auth/__pycache__/fastapi.cpython-310.pyc,,
chromadb/auth/__pycache__/fastapi_utils.cpython-310.pyc,,
chromadb/auth/__pycache__/providers.cpython-310.pyc,,
chromadb/auth/__pycache__/registry.cpython-310.pyc,,
chromadb/auth/authz/__init__.py,sha256=ypVY8QAmVbFXw_jMiJUx5PN3z73f0bL5rJiDobgOGsU,4142
chromadb/auth/authz/__pycache__/__init__.cpython-310.pyc,,
chromadb/auth/basic/__init__.py,sha256=BcSIjd_E98buS6f9aBOi-t4DxWJwkI4EE37YdjKBRoY,3682
chromadb/auth/basic/__pycache__/__init__.cpython-310.pyc,,
chromadb/auth/fastapi.py,sha256=EjAGdCloB_vVifXgeGs1P3iZGox0Cz02g9MCN22FHCc,12742
chromadb/auth/fastapi_utils.py,sha256=9PFOQBoRYuP5XF3cSjtE8g15IluY2DOELBWppkQMevU,1768
chromadb/auth/providers.py,sha256=Vl2Od921a01i9ac3tWIWcLc3IItKeP3BVUXkx--1Q8M,6874
chromadb/auth/registry.py,sha256=xMfhbkN60oDwCL_CpHOiHvcavpQpVNbUZ7-tlT3XWA8,5270
chromadb/auth/token/__init__.py,sha256=pqFOybZ01CRwkz7EomGCJo5XEYxNALGcLPdATAIqyTw,10613
chromadb/auth/token/__pycache__/__init__.cpython-310.pyc,,
chromadb/cli/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/cli/__pycache__/__init__.cpython-310.pyc,,
chromadb/cli/__pycache__/cli.cpython-310.pyc,,
chromadb/cli/__pycache__/utils.cpython-310.pyc,,
chromadb/cli/cli.py,sha256=Q3pBdG1fF6abUbWAyvbjdf_UxwZXim2BoJLz_ePKvgE,3414
chromadb/cli/utils.py,sha256=AI_HSweUI8WBU0ZAhPuQ1UInCBbPgstGh9QqF15QcLI,573
chromadb/config.py,sha256=JhiIGiswgvLcMO_etpLmrkBpjitx3JKNtdaLA8Sv8Z4,16938
chromadb/db/__init__.py,sha256=9ZuD_GMsCaZcmE6O777W9kukkgr7KTGkQQFPpU7tQ2M,3009
chromadb/db/__pycache__/__init__.cpython-310.pyc,,
chromadb/db/__pycache__/base.cpython-310.pyc,,
chromadb/db/__pycache__/migrations.cpython-310.pyc,,
chromadb/db/__pycache__/system.cpython-310.pyc,,
chromadb/db/base.py,sha256=4vM8TtIfqbdZigwYOxqNwlh9PoSvXyu_LMlbueM2j58,6004
chromadb/db/impl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/db/impl/__pycache__/__init__.cpython-310.pyc,,
chromadb/db/impl/__pycache__/sqlite.cpython-310.pyc,,
chromadb/db/impl/__pycache__/sqlite_pool.cpython-310.pyc,,
chromadb/db/impl/grpc/__pycache__/client.cpython-310.pyc,,
chromadb/db/impl/grpc/__pycache__/server.cpython-310.pyc,,
chromadb/db/impl/grpc/client.py,sha256=fmrP68ARQw1TalDXcA3TIMw7ce04SBI-zUIhBTXp15Y,10191
chromadb/db/impl/grpc/server.py,sha256=NXJ4u4RG62_ZQ6BhmQdbMPn14b-r5PBr6BL3X6mfYKQ,19495
chromadb/db/impl/sqlite.py,sha256=Nj_pY28Pj8vTyv_cxcqX1ZOpsBcpawx_H9X1AmCvbd0,8362
chromadb/db/impl/sqlite_pool.py,sha256=517mbvoNSK-QXuvPddGEVoUo49yr8TbsiEVaOlbx2eM,5105
chromadb/db/migrations.py,sha256=0hlsRHBd3K39QlWpENy5Bdc-CQ80IDguSYodc3kEz7U,9576
chromadb/db/mixins/__pycache__/embeddings_queue.cpython-310.pyc,,
chromadb/db/mixins/__pycache__/sysdb.cpython-310.pyc,,
chromadb/db/mixins/embeddings_queue.py,sha256=hTMTbgFQzfKkaBgSQkoPJyn_G9Hdsq4hBze-if82bv4,14442
chromadb/db/mixins/sysdb.py,sha256=XdNsueZ9kVAX1IBq5IjqEEO3zbfUwz2XAYxHNpGeAhw,27323
chromadb/db/system.py,sha256=H8Y9TQnFNCuGF_3FyQN5EgaV93awYQQKl-Nl1ru6oDE,4774
chromadb/errors.py,sha256=HrTS3yVedJhmXtVkUzb_xaiqW_hUrtgvQLMRKDivCws,1911
chromadb/experimental/density_relevance.ipynb,sha256=GS6nIb7E3xPBdrFCgOrsID8H5TYi9zWiSDQ2J2aZHWU,368700
chromadb/ingest/__init__.py,sha256=3KDC_Sb44oOGkUpd2Gt_VMC070oK7_6hKPybwAU80Jg,4583
chromadb/ingest/__pycache__/__init__.cpython-310.pyc,,
chromadb/ingest/impl/__pycache__/pulsar.cpython-310.pyc,,
chromadb/ingest/impl/__pycache__/pulsar_admin.cpython-310.pyc,,
chromadb/ingest/impl/__pycache__/simple_policy.cpython-310.pyc,,
chromadb/ingest/impl/__pycache__/utils.cpython-310.pyc,,
chromadb/ingest/impl/pulsar.py,sha256=l5-BDfKmOslEIHgxCQDz9pu44dG3Ey2WTDYFYLRd1nU,12018
chromadb/ingest/impl/pulsar_admin.py,sha256=quCqKc4yEYoYR7BAvTpYEFVk_5_eS2Nrk9sAtVjE5nI,3405
chromadb/ingest/impl/simple_policy.py,sha256=4HtW1Q22dGSiunWS_9UAvA0cTNgesctvW_3l0bY0nPI,2147
chromadb/ingest/impl/utils.py,sha256=u_LTE2SsWy3FwMFdSAVKAgicPfjhdF3LJ0VZ9q5WdKo,693
chromadb/log_config.yml,sha256=kK6gRC-h8KsuBHca4Hs_qPl_IM0CvZKOHoqKSAogVG8,921
chromadb/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/migrations/__pycache__/__init__.cpython-310.pyc,,
chromadb/migrations/embeddings_queue/00001-embeddings.sqlite.sql,sha256=-8vaxiHGvevlYa4L0-rIQHN6-CL07cDVZ7lOIGbuzyg,261
chromadb/migrations/metadb/00001-embedding-metadata.sqlite.sql,sha256=DnR35ivUCDDyj5mG8HA1l4F4d1AwRDJO_oyezIU5KEU,600
chromadb/migrations/metadb/00002-embedding-metadata.sqlite.sql,sha256=M33nC7icQr8BdHZC--kxD4zjhjWrUPLOgu6YrGZOdZs,334
chromadb/migrations/metadb/00003-full-text-tokenize.sqlite.sql,sha256=E_1YOYI8jx4jZDHC9xagrvAsW_2Aw1wVljSWkk6o3aQ,236
chromadb/migrations/sysdb/00001-collections.sqlite.sql,sha256=36VyD7KQiAyaerrJJiAkzTLacDoxJHG11k6ylhOgVb0,355
chromadb/migrations/sysdb/00002-segments.sqlite.sql,sha256=5Fmn9mj5yaOFYukiEf7szEVJ1aBKyilyOXKRSW1WShw,385
chromadb/migrations/sysdb/00003-collection-dimension.sqlite.sql,sha256=Rlyd1HO_CE5qzdz9FcJYfsrzJbMEOvSarsh-QAa96PM,54
chromadb/migrations/sysdb/00004-tenants-databases.sqlite.sql,sha256=DaWJfr1xpy7lQMEBEmVnH1726yHanqqh5YQI2KBIDcM,1228
chromadb/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/proto/__pycache__/__init__.cpython-310.pyc,,
chromadb/proto/__pycache__/chroma_pb2.cpython-310.pyc,,
chromadb/proto/__pycache__/chroma_pb2_grpc.cpython-310.pyc,,
chromadb/proto/__pycache__/convert.cpython-310.pyc,,
chromadb/proto/__pycache__/coordinator_pb2.cpython-310.pyc,,
chromadb/proto/__pycache__/coordinator_pb2_grpc.cpython-310.pyc,,
chromadb/proto/__pycache__/logservice_pb2.cpython-310.pyc,,
chromadb/proto/__pycache__/logservice_pb2_grpc.cpython-310.pyc,,
chromadb/proto/chroma_pb2.py,sha256=xOPXvL5tiy-VJcn2iTuI-KP0TPPxWy2hZdvALCKuzdY,6997
chromadb/proto/chroma_pb2.pyi,sha256=POeertvrW6CRrLX9hgDoXAd_SWsi-U2hXEELsb8IoJs,8583
chromadb/proto/chroma_pb2_grpc.py,sha256=TovVyZBcNgBtrCP4qsuc0c99TWGBnIohzPzXrhtbe0g,4222
chromadb/proto/convert.py,sha256=gITiO2L0zYoLiPQxF78AjuTYGqJtFSyTEWJNyEjCcKQ,9975
chromadb/proto/coordinator_pb2.py,sha256=DjqBB7IYSjAKiwCRr4GFdC3ozTnN-YNavEOM3oEfA_A,10245
chromadb/proto/coordinator_pb2.pyi,sha256=iVp_Uy7-pR8jDY_aCcs4-pxCG4oqHK_JHbjQjK4EKhQ,10642
chromadb/proto/coordinator_pb2_grpc.py,sha256=TQU8Jqrwk8uXsOGWAdO2H6PpB3xsRWeb8NjVUB9tNbc,23137
chromadb/proto/logservice_pb2.py,sha256=9aZoOd4d68O9Wg_km2LDjXKfBQqXPaGQ3Gpu53mHLAg,2420
chromadb/proto/logservice_pb2.pyi,sha256=C-64VC02xHoixsz8SpmdNudd23_9Nz_SAq7N-85tXlw,1839
chromadb/proto/logservice_pb2_grpc.py,sha256=e5LGIp00RHXf17ICeFjaf_dKwyKbgVIyd4n7sojoRBA,4260
chromadb/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/quota/__init__.py,sha256=ac8kKuST_A8vXnLyb_OuIfebuR7efRAmiXJggQuHx-g,4318
chromadb/quota/__pycache__/__init__.cpython-310.pyc,,
chromadb/quota/__pycache__/test_provider.cpython-310.pyc,,
chromadb/quota/test_provider.py,sha256=DXOhKT8db_qWwkYx3XozobORD0cad3LWELykuhrrdOg,383
chromadb/segment/__init__.py,sha256=t0v9LPzyg0ZjqoQ6g7ChxbqeQ5NZkwnT9SvRMmOI27Y,4281
chromadb/segment/__pycache__/__init__.cpython-310.pyc,,
chromadb/segment/distributed/__init__.py,sha256=XGJVTKBtjcaM0dH62VxnojbzvQ-NjqV38qeVC8nRwok,2442
chromadb/segment/distributed/__pycache__/__init__.cpython-310.pyc,,
chromadb/segment/impl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/__pycache__/__init__.cpython-310.pyc,,
chromadb/segment/impl/distributed/__pycache__/segment_directory.cpython-310.pyc,,
chromadb/segment/impl/distributed/__pycache__/server.cpython-310.pyc,,
chromadb/segment/impl/distributed/segment_directory.py,sha256=hl0Qm9-tfgYAEisY7RrN0ZNFuH5yeCihiPbheRXEQ8I,8631
chromadb/segment/impl/distributed/server.py,sha256=IU-1yOVVsTihjWvF_6jBehjL1EVRuGCZcjVdSIhFOmM,7756
chromadb/segment/impl/manager/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/manager/__pycache__/__init__.cpython-310.pyc,,
chromadb/segment/impl/manager/__pycache__/distributed.cpython-310.pyc,,
chromadb/segment/impl/manager/__pycache__/local.cpython-310.pyc,,
chromadb/segment/impl/manager/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/manager/cache/__pycache__/__init__.cpython-310.pyc,,
chromadb/segment/impl/manager/cache/__pycache__/cache.cpython-310.pyc,,
chromadb/segment/impl/manager/cache/cache.py,sha256=PwK8uKRoizttayJxkMyUE7oCkcPFQXvExMFHoLUAv3g,3007
chromadb/segment/impl/manager/distributed.py,sha256=nGEowPyevW8-F2h07QOSeJPDBgj5WcrPHWbOt7qTbCU,7496
chromadb/segment/impl/manager/local.py,sha256=kv8Wd4TyZn_9ZQlAsAwM06UmZRaHNXCOcvZEL-VS0sg,9920
chromadb/segment/impl/metadata/__pycache__/sqlite.cpython-310.pyc,,
chromadb/segment/impl/metadata/sqlite.py,sha256=zFHV0cFsnYok99hN98rvVRPPnAYy5hoaLU64rxS2c54,26785
chromadb/segment/impl/vector/__pycache__/batch.cpython-310.pyc,,
chromadb/segment/impl/vector/__pycache__/brute_force_index.cpython-310.pyc,,
chromadb/segment/impl/vector/__pycache__/grpc_segment.cpython-310.pyc,,
chromadb/segment/impl/vector/__pycache__/hnsw_params.cpython-310.pyc,,
chromadb/segment/impl/vector/__pycache__/local_hnsw.cpython-310.pyc,,
chromadb/segment/impl/vector/__pycache__/local_persistent_hnsw.cpython-310.pyc,,
chromadb/segment/impl/vector/batch.py,sha256=WcNRyD-szkO1mOoySvkREsQk9BYTEftIsj9G_HdyaO0,4170
chromadb/segment/impl/vector/brute_force_index.py,sha256=-jFj0Nr9r5Bm2Xp_EyITL18tRwoJfGdETB3pnI84mq4,5521
chromadb/segment/impl/vector/grpc_segment.py,sha256=u8F292iOFGPSRF0QI4ApW4Aof3L9l71b08ItvpxNGXM,3649
chromadb/segment/impl/vector/hnsw_params.py,sha256=rS2b9LVfsqkn20OYnlERMP80jias34LBggKk5kPp5L8,3161
chromadb/segment/impl/vector/local_hnsw.py,sha256=qbPbShGsGID9Ovmjzyczyw_174yllCeMcO6GXrw7W_c,11673
chromadb/segment/impl/vector/local_persistent_hnsw.py,sha256=PquSWioQxrBbRoKVKvT2YSDhlxYwI_QGRgwyfw_ATAc,18868
chromadb/server/__init__.py,sha256=KfAHxJipqapFZd2ijNH9-L9xQMDg8Q349xM8hY72hS8,172
chromadb/server/__pycache__/__init__.cpython-310.pyc,,
chromadb/server/fastapi/__init__.py,sha256=J0rww4wVE14AlLYhGCS2d_ZOCoV4bAEVBGGTDKnRdXE,22281
chromadb/server/fastapi/__pycache__/__init__.cpython-310.pyc,,
chromadb/server/fastapi/__pycache__/types.cpython-310.pyc,,
chromadb/server/fastapi/types.py,sha256=Im2IPj8r7yI5wPCDQ14xLjgiL26O1VZvYf6vhNwYZLQ,2126
chromadb/telemetry/README.md,sha256=4P1G-ZE1OhJfyv5Wm3KW8NmGONLtUT10tQrj-in1aDc,587
chromadb/telemetry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/telemetry/__pycache__/__init__.cpython-310.pyc,,
chromadb/telemetry/opentelemetry/__init__.py,sha256=vo-aPIoQPij0Ymq9ZdJVSfw2cMy0_NknHC49ia2Nqz0,4998
chromadb/telemetry/opentelemetry/__pycache__/__init__.cpython-310.pyc,,
chromadb/telemetry/opentelemetry/__pycache__/fastapi.cpython-310.pyc,,
chromadb/telemetry/opentelemetry/fastapi.py,sha256=-h-OzcgP8A9nKLCqJ4TPN-1KBE5p34wLB-NUYh1vKdg,405
chromadb/telemetry/product/__init__.py,sha256=jJeOw_Exieq-FgqNobxj851KkKnH4l8MuvL7PJ2mj-k,2786
chromadb/telemetry/product/__pycache__/__init__.cpython-310.pyc,,
chromadb/telemetry/product/__pycache__/events.cpython-310.pyc,,
chromadb/telemetry/product/__pycache__/posthog.cpython-310.pyc,,
chromadb/telemetry/product/events.py,sha256=bfJy4h2tX8VzHc85dWal7D174dw9_b3bEunaYs-NygQ,8100
chromadb/telemetry/product/posthog.py,sha256=cEVcKcGDU2ZQng5_nS3jlth1WhLlsUXs9uG0jL7_Jdw,2089
chromadb/test/__pycache__/conftest.cpython-310.pyc,,
chromadb/test/__pycache__/test_api.cpython-310.pyc,,
chromadb/test/__pycache__/test_chroma.cpython-310.pyc,,
chromadb/test/__pycache__/test_cli.cpython-310.pyc,,
chromadb/test/__pycache__/test_client.cpython-310.pyc,,
chromadb/test/__pycache__/test_config.cpython-310.pyc,,
chromadb/test/__pycache__/test_multithreaded.cpython-310.pyc,,
chromadb/test/api/__pycache__/test_types.cpython-310.pyc,,
chromadb/test/api/test_types.py,sha256=EEZk7nqJcp4diwnRRcSaf4sic4_A41LiDPXIfmwT-2k,1271
chromadb/test/auth/__pycache__/test_basic_auth.cpython-310.pyc,,
chromadb/test/auth/__pycache__/test_simple_rbac_authz.cpython-310.pyc,,
chromadb/test/auth/__pycache__/test_token_auth.cpython-310.pyc,,
chromadb/test/auth/test_basic_auth.py,sha256=azZ0LyXaE2E_wyyXK9moHS3PpoQ-Rrn_TRhU6N3YCRM,313
chromadb/test/auth/test_simple_rbac_authz.py,sha256=mlYTq_S-lAfmYUJfWjhf5miCFltPmQtRo-LOTNmzIcM,11498
chromadb/test/auth/test_token_auth.py,sha256=awp6WuF5g8sqEh9SdOvva4JmiliDNXHpRkA8Htp7mvc,5078
chromadb/test/client/__pycache__/create_http_client_with_basic_auth.cpython-310.pyc,,
chromadb/test/client/__pycache__/test_cloud_client.cpython-310.pyc,,
chromadb/test/client/__pycache__/test_create_http_client.cpython-310.pyc,,
chromadb/test/client/__pycache__/test_database_tenant.cpython-310.pyc,,
chromadb/test/client/__pycache__/test_multiple_clients_concurrency.cpython-310.pyc,,
chromadb/test/client/create_http_client_with_basic_auth.py,sha256=5OD0j0_4WNRUmBwdHegx3GHYJeB3C8o-kjoEtWt91AE,776
chromadb/test/client/test_cloud_client.py,sha256=numZ5YhaiBoVv0Z0Q8kBO-UiRAX1nzbOMAwwtXhcfFE,3269
chromadb/test/client/test_create_http_client.py,sha256=cxRy-4uT62y1BsnTEHZOW0o1C79DNN9zzmI3wWkBjMk,557
chromadb/test/client/test_database_tenant.py,sha256=6TTP_3tCWmgAMY_KzRwkBFrPoGtW-uwz_2KgK2lD5Rs,6304
chromadb/test/client/test_multiple_clients_concurrency.py,sha256=YpqtBWoKzAxToeBSlZhSQV6K_X3FwXYrN2JVNz80Qrw,1850
chromadb/test/conftest.py,sha256=LCT6A1-W2ytDBP3lfJl5AYpwWlEv2M07BvJJAMN9MdM,20588
chromadb/test/data_loader/__pycache__/test_data_loader.cpython-310.pyc,,
chromadb/test/data_loader/test_data_loader.py,sha256=kVg4VKQa8PqwBrHb_L8j5hK7D3EvU0rYKu5qWlzwzzQ,3613
chromadb/test/db/__pycache__/test_base.cpython-310.pyc,,
chromadb/test/db/__pycache__/test_hash.cpython-310.pyc,,
chromadb/test/db/__pycache__/test_migrations.cpython-310.pyc,,
chromadb/test/db/__pycache__/test_system.cpython-310.pyc,,
chromadb/test/db/migrations/00001-migration-1.psql.sql,sha256=IsEhKzPoC8iDGlDDoADnq6ReDnSDpEfmc-_qR00kp8g,51
chromadb/test/db/migrations/00001-migration-1.sqlite.sql,sha256=IsEhKzPoC8iDGlDDoADnq6ReDnSDpEfmc-_qR00kp8g,51
chromadb/test/db/migrations/00002-migration-2.psql.sql,sha256=WP1wmiaaFn1vXY0q4Zep8NA5PL_twDjf-7dNDJqFt98,51
chromadb/test/db/migrations/00002-migration-2.sqlite.sql,sha256=WP1wmiaaFn1vXY0q4Zep8NA5PL_twDjf-7dNDJqFt98,51
chromadb/test/db/migrations/00003-migration-3.psql.sql,sha256=JGDEfNpYlva79BCqlCiYWX8H3O24GcaKNfKx7KbSyvg,51
chromadb/test/db/migrations/00003-migration-3.sqlite.sql,sha256=JGDEfNpYlva79BCqlCiYWX8H3O24GcaKNfKx7KbSyvg,51
chromadb/test/db/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/test/db/migrations/__pycache__/__init__.cpython-310.pyc,,
chromadb/test/db/test_base.py,sha256=euzzrJGWQlyiqZaBUcpNIL4I-xI9nRDvHaTsu8Sf6KE,1168
chromadb/test/db/test_hash.py,sha256=wQpH8Mziu1yhSXmc9LQ7Jd4DutPxrtpYJ8v-kuSOviY,4202
chromadb/test/db/test_migrations.py,sha256=DIraBgJC5-8mE_hHHJsj4wKve1HhB7D4mZSkazlIN2E,5006
chromadb/test/db/test_system.py,sha256=jtPaD-TFMH13l09NDvWUEaF0jxJU1_1231kX-nD0Vks,26443
chromadb/test/ef/__pycache__/test_default_ef.cpython-310.pyc,,
chromadb/test/ef/__pycache__/test_multimodal_ef.cpython-310.pyc,,
chromadb/test/ef/test_default_ef.py,sha256=-TVmfAxG-XDaIbnWmatvO6ZvsgNDrfjlXMF8W-SBvF8,2720
chromadb/test/ef/test_multimodal_ef.py,sha256=wAvu_17jzhgCX4uTWUabn2JB0SMhGM7h1i4e3QbeNF0,5587
chromadb/test/ingest/__pycache__/test_producer_consumer.cpython-310.pyc,,
chromadb/test/ingest/test_producer_consumer.py,sha256=T0xKe6ResnwSzm3uzC3i6Hvl-bBOjaDw4AJEPI_0Eaw,13622
chromadb/test/openssl.cnf,sha256=AzVbikFN8k5pCTkCE-hA4C8l2BS0q6N6BlGIeVJPwp0,188
chromadb/test/property/__pycache__/invariants.cpython-310.pyc,,
chromadb/test/property/__pycache__/strategies.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_add.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_client_url.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_collections.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_collections_with_database_tenant.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_cross_version_persist.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_embeddings.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_filtering.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_persist.cpython-310.pyc,,
chromadb/test/property/__pycache__/test_segment_manager.cpython-310.pyc,,
chromadb/test/property/invariants.py,sha256=LiZnwEwVNjpdiXG2Ogc5nKNtW-GwxGUedh85IUd7750,11885
chromadb/test/property/strategies.py,sha256=96LYG4OlfshCnYZ9Ii4GOj5CloMY5s--gCh_9SuCqxg,22248
chromadb/test/property/test_add.py,sha256=K-23r9iz5RYFEZtVUTtK35-euBfPiw1RskpGxjkRZYk,5829
chromadb/test/property/test_client_url.py,sha256=PjT9vHBnSawQ9J2VhmS1UDbZpAKMLo52Z3IzuSLNnR4,3881
chromadb/test/property/test_collections.py,sha256=NTIfATjBteHGtxuRVA843anY7v_H-xz3e0QWARcdmUQ,11779
chromadb/test/property/test_collections_with_database_tenant.py,sha256=9Z90zrEW9B2r6GITz7KiaSwKBUXtzgZnWbRqxB1eQag,4095
chromadb/test/property/test_cross_version_persist.py,sha256=EumWWhpaKrNDz1h2ARQwciNQcbctksWJq-HmgjnRXFI,11569
chromadb/test/property/test_embeddings.py,sha256=rcUgl4hdHub63gAWTBJFAm58mKvICs28BAs6DVALQJc,17215
chromadb/test/property/test_filtering.py,sha256=_kuyTE9gsDA0QXAoxLM43cjmU29F62PH1xprgrMCVHw,12867
chromadb/test/property/test_persist.py,sha256=0qi4uzaIcv8G0znydcW2yGdohXEqJMO7v_EPpDSA19I,7078
chromadb/test/property/test_segment_manager.py,sha256=5TAXutJZQfjNQMZ69KLx-lV39ByFv2jOQIDVDiUG7u8,4397
chromadb/test/quota/__pycache__/test_static_quota_enforcer.cpython-310.pyc,,
chromadb/test/quota/test_static_quota_enforcer.py,sha256=IEmnASXpIYYs3wj3l17YirM_OoUG_Pv03MLST5crjS8,3008
chromadb/test/segment/__pycache__/test_metadata.cpython-310.pyc,,
chromadb/test/segment/__pycache__/test_vector.cpython-310.pyc,,
chromadb/test/segment/distributed/__pycache__/test_memberlist_provider.cpython-310.pyc,,
chromadb/test/segment/distributed/__pycache__/test_rendezvous_hash.cpython-310.pyc,,
chromadb/test/segment/distributed/test_memberlist_provider.py,sha256=TcxucMg1NSzpC6VpSxd-RGxOxsn4oFJWr0K4-EMyK5o,3470
chromadb/test/segment/distributed/test_rendezvous_hash.py,sha256=25EkhbNy-wGuSJ5Pra2hfU7IOrap-2NL7aUICPoheOA,907
chromadb/test/segment/test_metadata.py,sha256=FhOVSO8TGe-IF3kpwNAxOfDTYvZN96JP3viDqZ9wcLE,20690
chromadb/test/segment/test_vector.py,sha256=Gcx-f6Ugmcq80wraFfxnGh9SsiL0D6bGgG_5KZ4Paaw,20927
chromadb/test/stress/__pycache__/test_many_collections.cpython-310.pyc,,
chromadb/test/stress/test_many_collections.py,sha256=7QO3lO1puPRXdK7VWAGYEgVzl1l5ZCSZelESfLhGAj4,1105
chromadb/test/test_api.py,sha256=eBjBXmJzoo8050KxFlj8tJsHCHoWvhO50Af5sY-MPTE,44851
chromadb/test/test_chroma.py,sha256=UGa36kNijYd351k9xkoA230Atr4bDcSV4GmA4_2Z8R4,4197
chromadb/test/test_cli.py,sha256=jSIZgeY0MHDelVa83c1LF8BQvH7KRYP8IdiPEyePE1U,642
chromadb/test/test_client.py,sha256=Brj_-zPDpFN7W4F6Pj5t2fA42ypz_tW6SnLYbRiV0Zw,2082
chromadb/test/test_config.py,sha256=_GpZzYfXPagtWf3sV9aHBKbI0gedzj_xnewMzA2Uxas,4127
chromadb/test/test_multithreaded.py,sha256=rkvBrhJVBXMvP2T74U7oGoag_BG4jnYz1C7BEdWxfnc,8241
chromadb/test/utils/__pycache__/test_messagid.cpython-310.pyc,,
chromadb/test/utils/test_messagid.py,sha256=Prgq0IpITdg4y31nCgP6M-xKftyWqFsIcPVCpEguf40,3544
chromadb/types.py,sha256=nb4jlzNqC_Z1KHi1wDRewxsc2CUUr3vtzznE8ybsN-4,4790
chromadb/utils/__init__.py,sha256=-t25WU4oRKXU3I4PF8G_uVM2UNzu1SfjfS1DL2P0WVQ,378
chromadb/utils/__pycache__/__init__.cpython-310.pyc,,
chromadb/utils/__pycache__/batch_utils.cpython-310.pyc,,
chromadb/utils/__pycache__/data_loaders.cpython-310.pyc,,
chromadb/utils/__pycache__/delete_file.cpython-310.pyc,,
chromadb/utils/__pycache__/directory.cpython-310.pyc,,
chromadb/utils/__pycache__/distance_functions.cpython-310.pyc,,
chromadb/utils/__pycache__/embedding_functions.cpython-310.pyc,,
chromadb/utils/__pycache__/fastapi.cpython-310.pyc,,
chromadb/utils/__pycache__/lru_cache.cpython-310.pyc,,
chromadb/utils/__pycache__/messageid.cpython-310.pyc,,
chromadb/utils/__pycache__/read_write_lock.cpython-310.pyc,,
chromadb/utils/__pycache__/rendezvous_hash.cpython-310.pyc,,
chromadb/utils/batch_utils.py,sha256=0AJL8aQtVrfm3TWr1zdN6CxXlXkj6C2XZM4IZTvUbvQ,1163
chromadb/utils/data_loaders.py,sha256=9ftDeCIp9-cVdCGJeE8kaUleqF6ey7Okqrmcqwphf7o,1006
chromadb/utils/delete_file.py,sha256=FUqldkeqFQE5O2S-aH4zEb27JwZTQ2_PK7sqMRHpN74,1130
chromadb/utils/directory.py,sha256=McVtw14fWMFdcfUy4kz29MLMShFvzEOokOsm75tiUjg,620
chromadb/utils/distance_functions.py,sha256=h7C5v23DkiZ1_jT2v87cIeR7E02erBop_dhhbVOiGmo,657
chromadb/utils/embedding_functions.py,sha256=0fFiorl6P0iXuwWU5vtFIyqNo_y9N_5OUw4XJSbo3Z4,32691
chromadb/utils/fastapi.py,sha256=LIYvUjwBVxWsXavC50wLnkipp-5_Owolwp_f6MvKccQ,504
chromadb/utils/lru_cache.py,sha256=7szY8FsguzNDD5eXU0RmJjxmAxXAYCag77lv0pWagdQ,1076
chromadb/utils/messageid.py,sha256=bjTAumbhoYL7gX9-ouViupU_uzYMXNYAaIQphGAFu5Q,2301
chromadb/utils/read_write_lock.py,sha256=AxZ7GdYC9g05ZLmzLKTw051_EbvEQ8F9CIYpONcYDbI,2010
chromadb/utils/rendezvous_hash.py,sha256=zItJNYAhvMi8vZnd0eaQiRhG4YFBO4RKsMR3gFtMUlI,1445
