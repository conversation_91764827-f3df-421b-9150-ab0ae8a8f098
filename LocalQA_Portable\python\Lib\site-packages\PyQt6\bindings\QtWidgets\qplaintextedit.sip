// qplaintextedit.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPlainTextEdit : public QAbstractScrollArea
{
%TypeHeaderCode
#include <qplaintextedit.h>
%End

public:
    enum LineWrapMode
    {
        NoWrap,
        WidgetWidth,
    };

    explicit QPlainTextEdit(QWidget *parent /TransferThis/ = 0);
    QPlainTextEdit(const QString &text, QWidget *parent /TransferThis/ = 0);
    virtual ~QPlainTextEdit();
    void setDocument(QTextDocument *document /KeepReference/);
    QTextDocument *document() const;
    void setTextCursor(const QTextCursor &cursor);
    QTextCursor textCursor() const;
    bool isReadOnly() const;
    void setReadOnly(bool ro);
    void setTextInteractionFlags(Qt::TextInteractionFlags flags);
    Qt::TextInteractionFlags textInteractionFlags() const;
    void mergeCurrentCharFormat(const QTextCharFormat &modifier);
    void setCurrentCharFormat(const QTextCharFormat &format);
    QTextCharFormat currentCharFormat() const;
    bool tabChangesFocus() const;
    void setTabChangesFocus(bool b);
    void setDocumentTitle(const QString &title);
    QString documentTitle() const;
    bool isUndoRedoEnabled() const;
    void setUndoRedoEnabled(bool enable);
    void setMaximumBlockCount(int maximum);
    int maximumBlockCount() const;
    QPlainTextEdit::LineWrapMode lineWrapMode() const;
    void setLineWrapMode(QPlainTextEdit::LineWrapMode mode);
    QTextOption::WrapMode wordWrapMode() const;
    void setWordWrapMode(QTextOption::WrapMode policy);
    void setBackgroundVisible(bool visible);
    bool backgroundVisible() const;
    void setCenterOnScroll(bool enabled);
    bool centerOnScroll() const;
    bool find(const QString &exp, QTextDocument::FindFlags options = QTextDocument::FindFlags());
    QString toPlainText() const;
    void ensureCursorVisible();
    virtual QVariant loadResource(int type, const QUrl &name);
    QMenu *createStandardContextMenu() /Factory/;
    QMenu *createStandardContextMenu(const QPoint &position) /Factory/;
    QTextCursor cursorForPosition(const QPoint &pos) const;
    QRect cursorRect(const QTextCursor &cursor) const;
    QRect cursorRect() const;
    bool overwriteMode() const;
    void setOverwriteMode(bool overwrite);
    int cursorWidth() const;
    void setCursorWidth(int width);
    void setExtraSelections(const QList<QTextEdit::ExtraSelection> &selections);
    QList<QTextEdit::ExtraSelection> extraSelections() const;
    void moveCursor(QTextCursor::MoveOperation operation, QTextCursor::MoveMode mode = QTextCursor::MoveAnchor);
    bool canPaste() const;
%If (PyQt_Printer)
    void print(QPagedPaintDevice *printer) const;
%End
    int blockCount() const;

public slots:
    void setPlainText(const QString &text);
    void cut();
    void copy();
    void paste();
    void undo();
    void redo();
    void clear();
    void selectAll();
    void insertPlainText(const QString &text);
    void appendPlainText(const QString &text);
    void appendHtml(const QString &html);
    void centerCursor();

signals:
    void textChanged();
    void undoAvailable(bool b);
    void redoAvailable(bool b);
    void copyAvailable(bool b);
    void selectionChanged();
    void cursorPositionChanged();
    void updateRequest(const QRect &rect, int dy);
    void blockCountChanged(int newBlockCount);
    void modificationChanged(bool);

protected:
    virtual bool event(QEvent *e);
    virtual void timerEvent(QTimerEvent *e);
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void keyReleaseEvent(QKeyEvent *e);
    virtual void resizeEvent(QResizeEvent *e);
    virtual void paintEvent(QPaintEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void mouseDoubleClickEvent(QMouseEvent *e);
    virtual bool focusNextPrevChild(bool next);
    virtual void contextMenuEvent(QContextMenuEvent *e);
    virtual void dragEnterEvent(QDragEnterEvent *e);
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
    virtual void dragMoveEvent(QDragMoveEvent *e);
    virtual void dropEvent(QDropEvent *e);
    virtual void focusInEvent(QFocusEvent *e);
    virtual void focusOutEvent(QFocusEvent *e);
    virtual void showEvent(QShowEvent *);
    virtual void changeEvent(QEvent *e);
    virtual void wheelEvent(QWheelEvent *e);
    virtual void inputMethodEvent(QInputMethodEvent *);

public:
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery property) const;

protected:
    virtual QMimeData *createMimeDataFromSelection() const /Factory/;
    virtual bool canInsertFromMimeData(const QMimeData *source) const;
    virtual void insertFromMimeData(const QMimeData *source);
    virtual void scrollContentsBy(int dx, int dy);
    QTextBlock firstVisibleBlock() const;
    QPointF contentOffset() const;
    QRectF blockBoundingRect(const QTextBlock &block) const;
    QRectF blockBoundingGeometry(const QTextBlock &block) const;
    QAbstractTextDocumentLayout::PaintContext getPaintContext() const;

public:
    QString anchorAt(const QPoint &pos) const;

public slots:
    void zoomIn(int range = 1);
    void zoomOut(int range = 1);

public:
    void setPlaceholderText(const QString &placeholderText);
    QString placeholderText() const;
    bool find(const QRegularExpression &exp, QTextDocument::FindFlags options = QTextDocument::FindFlags());
    QVariant inputMethodQuery(Qt::InputMethodQuery query, QVariant argument) const;
    qreal tabStopDistance() const;
    void setTabStopDistance(qreal distance);
};

class QPlainTextDocumentLayout : public QAbstractTextDocumentLayout
{
%TypeHeaderCode
#include <qplaintextedit.h>
%End

public:
    QPlainTextDocumentLayout(QTextDocument *document);
    virtual ~QPlainTextDocumentLayout();
    virtual void draw(QPainter *, const QAbstractTextDocumentLayout::PaintContext &);
    virtual int hitTest(const QPointF &, Qt::HitTestAccuracy) const;
    virtual int pageCount() const;
    virtual QSizeF documentSize() const;
    virtual QRectF frameBoundingRect(QTextFrame *) const;
    virtual QRectF blockBoundingRect(const QTextBlock &block) const;
    void ensureBlockLayout(const QTextBlock &block) const;
    void setCursorWidth(int width);
    int cursorWidth() const;
    void requestUpdate();

protected:
    virtual void documentChanged(int from, int, int charsAdded);
};
