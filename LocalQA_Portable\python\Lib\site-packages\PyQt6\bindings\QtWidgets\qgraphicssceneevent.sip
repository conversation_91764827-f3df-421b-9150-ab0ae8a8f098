// qgraphicssceneevent.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsSceneEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QEvent::GraphicsSceneContextMenu:
        sipType = sipType_QGraphicsSceneContextMenuEvent;
        break;
       
    case QEvent::GraphicsSceneDragEnter:
    case QEvent::GraphicsSceneDragLeave:
    case QEvent::GraphicsSceneDragMove:
    case QEvent::GraphicsSceneDrop:
        sipType = sipType_QGraphicsSceneDragDropEvent;
        break;
       
    case QEvent::GraphicsSceneHelp:
        sipType = sipType_QGraphicsSceneHelpEvent;
        break;
       
    case QEvent::GraphicsSceneHoverEnter:
    case QEvent::GraphicsSceneHoverLeave:
    case QEvent::GraphicsSceneHoverMove:
        sipType = sipType_QGraphicsSceneHoverEvent;
        break;
       
    case QEvent::GraphicsSceneMouseDoubleClick:
    case QEvent::GraphicsSceneMouseMove:
    case QEvent::GraphicsSceneMousePress:
    case QEvent::GraphicsSceneMouseRelease:
        sipType = sipType_QGraphicsSceneMouseEvent;
        break;
       
    case QEvent::GraphicsSceneWheel:
        sipType = sipType_QGraphicsSceneWheelEvent;
        break;
       
    case QEvent::GraphicsSceneMove:
        sipType = sipType_QGraphicsSceneMoveEvent;
        break;
    
    case QEvent::GraphicsSceneResize:
        sipType = sipType_QGraphicsSceneResizeEvent;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    virtual ~QGraphicsSceneEvent();
    QWidget *widget() const;
%If (Qt_6_2_0 -)
    quint64 timestamp() const;
%End

private:
    QGraphicsSceneEvent(const QGraphicsSceneEvent &);
};

class QGraphicsSceneMouseEvent : public QGraphicsSceneEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    virtual ~QGraphicsSceneMouseEvent();
    QPointF pos() const;
    QPointF scenePos() const;
    QPoint screenPos() const;
    QPointF buttonDownPos(Qt::MouseButton button) const;
    QPointF buttonDownScenePos(Qt::MouseButton button) const;
    QPoint buttonDownScreenPos(Qt::MouseButton button) const;
    QPointF lastPos() const;
    QPointF lastScenePos() const;
    QPoint lastScreenPos() const;
    Qt::MouseButtons buttons() const;
    Qt::MouseButton button() const;
    Qt::KeyboardModifiers modifiers() const;
    Qt::MouseEventSource source() const;
    Qt::MouseEventFlags flags() const;
};

class QGraphicsSceneWheelEvent : public QGraphicsSceneEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    virtual ~QGraphicsSceneWheelEvent();
    QPointF pos() const;
    QPointF scenePos() const;
    QPoint screenPos() const;
    Qt::MouseButtons buttons() const;
    Qt::KeyboardModifiers modifiers() const;
    int delta() const;
    Qt::Orientation orientation() const;
%If (Qt_6_2_0 -)
    Qt::ScrollPhase phase() const;
%End
%If (Qt_6_2_0 -)
    QPoint pixelDelta() const;
%End
%If (Qt_6_2_0 -)
    bool isInverted() const;
%End
};

class QGraphicsSceneContextMenuEvent : public QGraphicsSceneEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    enum Reason
    {
        Mouse,
        Keyboard,
        Other,
    };

    virtual ~QGraphicsSceneContextMenuEvent();
    QPointF pos() const;
    QPointF scenePos() const;
    QPoint screenPos() const;
    Qt::KeyboardModifiers modifiers() const;
    QGraphicsSceneContextMenuEvent::Reason reason() const;
};

class QGraphicsSceneHoverEvent : public QGraphicsSceneEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    virtual ~QGraphicsSceneHoverEvent();
    QPointF pos() const;
    QPointF scenePos() const;
    QPoint screenPos() const;
    QPointF lastPos() const;
    QPointF lastScenePos() const;
    QPoint lastScreenPos() const;
    Qt::KeyboardModifiers modifiers() const;
};

class QGraphicsSceneHelpEvent : public QGraphicsSceneEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    virtual ~QGraphicsSceneHelpEvent();
    QPointF scenePos() const;
    QPoint screenPos() const;
};

class QGraphicsSceneDragDropEvent : public QGraphicsSceneEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    virtual ~QGraphicsSceneDragDropEvent();
    QPointF pos() const;
    QPointF scenePos() const;
    QPoint screenPos() const;
    Qt::MouseButtons buttons() const;
    Qt::KeyboardModifiers modifiers() const;
    Qt::DropActions possibleActions() const;
    Qt::DropAction proposedAction() const;
    void acceptProposedAction();
    Qt::DropAction dropAction() const;
    void setDropAction(Qt::DropAction action);
    QWidget *source() const;
    const QMimeData *mimeData() const;
};

class QGraphicsSceneResizeEvent : public QGraphicsSceneEvent
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    QGraphicsSceneResizeEvent();
    virtual ~QGraphicsSceneResizeEvent();
    QSizeF oldSize() const;
    QSizeF newSize() const;
};

class QGraphicsSceneMoveEvent : public QGraphicsSceneEvent
{
%TypeHeaderCode
#include <qgraphicssceneevent.h>
%End

public:
    QGraphicsSceneMoveEvent();
    virtual ~QGraphicsSceneMoveEvent();
    QPointF oldPos() const;
    QPointF newPos() const;
};
