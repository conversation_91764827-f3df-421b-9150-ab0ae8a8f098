layoutparser-0.3.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
layoutparser-0.3.4.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
layoutparser-0.3.4.dist-info/METADATA,sha256=TXKiRsj8Qnhe3V0agioHRYWTTD7RblTJzd3VB9qTBZI,7725
layoutparser-0.3.4.dist-info/RECORD,,
layoutparser-0.3.4.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
layoutparser-0.3.4.dist-info/top_level.txt,sha256=iFU1TeYXJ0p3IPcOaR-Ww84gM6yfA84s2RKhMRWP9UA,13
layoutparser/__init__.py,sha256=vkNC7tZXxM0hBRz0PQLTovGNxoCpGqGzPAog3rtZPR4,2357
layoutparser/__pycache__/__init__.cpython-310.pyc,,
layoutparser/__pycache__/file_utils.cpython-310.pyc,,
layoutparser/__pycache__/visualization.cpython-310.pyc,,
layoutparser/elements/__init__.py,sha256=74XOK5N-0-d4vPWT9veG9fcH65nr9Ug2K79kiYuK7kU,876
layoutparser/elements/__pycache__/__init__.cpython-310.pyc,,
layoutparser/elements/__pycache__/base.cpython-310.pyc,,
layoutparser/elements/__pycache__/errors.cpython-310.pyc,,
layoutparser/elements/__pycache__/layout.cpython-310.pyc,,
layoutparser/elements/__pycache__/layout_elements.cpython-310.pyc,,
layoutparser/elements/__pycache__/utils.cpython-310.pyc,,
layoutparser/elements/base.py,sha256=XgTe6inUmTdEaw9S4TkJoaSz4kYA7-Rg1MDQJHbTvdI,9687
layoutparser/elements/errors.py,sha256=sSUOJBEi5BZj7oKDayaETuCD-Ssb7Zc4F6dP4t0RZYg,1164
layoutparser/elements/layout.py,sha256=0Woy3YwbE7vYIqjnW_kbwOEnop_MLpX0uN9ESN5tQ4Q,12997
layoutparser/elements/layout_elements.py,sha256=u82gpeERUrKCOC3S83fyYC8foL_cTJS5L7UgHmXq5RA,45943
layoutparser/elements/utils.py,sha256=ddckY5UT6NSgWvsoMKbtC5lyAU1hsKu7eLLNLw5KGx8,2767
layoutparser/file_utils.py,sha256=9S-oyMlq_NJC9cSskCr0946mjB45BsBoydZG54wsxnM,8476
layoutparser/io/__init__.py,sha256=22d7yDwkpVbzecDUEDmLRyaNEJa0bbtd75iPk1vSQW0,91
layoutparser/io/__pycache__/__init__.cpython-310.pyc,,
layoutparser/io/__pycache__/basic.cpython-310.pyc,,
layoutparser/io/__pycache__/pdf.cpython-310.pyc,,
layoutparser/io/basic.py,sha256=gUpzYIrhBZiibLNEMxpTN9vhXh4O5fDQqDvd71FDkM4,4915
layoutparser/io/pdf.py,sha256=D8sndjS8jzB-yFq_mYzCxdRzSoWA41p4BtkC_fr15K4,9212
layoutparser/misc/NotoSerifCJKjp-Regular.otf,sha256=BqFSzzisJKQG2KSsgROQkuqrNiwHqIDvjrmPVJc6t9E,23607780
layoutparser/models/__init__.py,sha256=NJhRUIqrHrAHpba68YDg-W1qp-Cri2CGU8FRFEGig5s,836
layoutparser/models/__pycache__/__init__.cpython-310.pyc,,
layoutparser/models/__pycache__/auto_layoutmodel.cpython-310.pyc,,
layoutparser/models/__pycache__/base_catalog.cpython-310.pyc,,
layoutparser/models/__pycache__/base_layoutmodel.cpython-310.pyc,,
layoutparser/models/__pycache__/model_config.cpython-310.pyc,,
layoutparser/models/auto_layoutmodel.py,sha256=hUtrJpiS1K7bUx-mjR3fcqs7KODE6BUHLYXrTQ4uCsU,2841
layoutparser/models/base_catalog.py,sha256=8x8cr4JrkigA-kn6C1SKJVoscwnyohS6dqZdSUNxqts,1213
layoutparser/models/base_layoutmodel.py,sha256=0O6bP5_x7LWW2OiXITsMzXhwpC_raw5kPjz3MvQDyZk,3597
layoutparser/models/detectron2/__init__.py,sha256=BCCjY0KaET2AGQe5AoD0Z3TUELXUG4doP4lGfTHB_n0,844
layoutparser/models/detectron2/__pycache__/__init__.cpython-310.pyc,,
layoutparser/models/detectron2/__pycache__/catalog.cpython-310.pyc,,
layoutparser/models/detectron2/__pycache__/layoutmodel.cpython-310.pyc,,
layoutparser/models/detectron2/catalog.py,sha256=kCvAuVH1BiN97AsIXGhwdXeAiWtlLGwGsLG-XZYW-wU,5018
layoutparser/models/detectron2/layoutmodel.py,sha256=ut46DQqECeynMKDV6ZqUDoA2KWa5K9YbNIdT4AT2WHg,5502
layoutparser/models/effdet/__init__.py,sha256=kBUwK8BFFWZ-t1bloA3nUV1EU3t-PyOyRtl6tdA8i7M,691
layoutparser/models/effdet/__pycache__/__init__.cpython-310.pyc,,
layoutparser/models/effdet/__pycache__/catalog.cpython-310.pyc,,
layoutparser/models/effdet/__pycache__/layoutmodel.cpython-310.pyc,,
layoutparser/models/effdet/catalog.py,sha256=X0IM5RMCSBigWfast1dzVxAflYfreemItkmlYu3s7fI,2338
layoutparser/models/effdet/layoutmodel.py,sha256=XsnxIRKc-c7OdlgFmdl6hqzHP_diTMYMFfWCm91UJ7k,8795
layoutparser/models/model_config.py,sha256=yIhRkYrB3fex_BHvMqQqqjxgTSFR7lN_7vqc7i46X6E,4575
layoutparser/models/paddledetection/__init__.py,sha256=MFaN-ZJtzNEso1KSgnXwAplWwvB8tukAvMEh7yPbAE4,737
layoutparser/models/paddledetection/__pycache__/__init__.cpython-310.pyc,,
layoutparser/models/paddledetection/__pycache__/catalog.cpython-310.pyc,,
layoutparser/models/paddledetection/__pycache__/layoutmodel.cpython-310.pyc,,
layoutparser/models/paddledetection/catalog.py,sha256=g7qwEsLdXz-Ure2PIWzlDGWuu-g_qC7tDa8gH7km3xQ,7622
layoutparser/models/paddledetection/layoutmodel.py,sha256=NNl0Y5-fR-CHV7pwEY0LBiJ_thihrXLm3XMBF_X-ewQ,10132
layoutparser/ocr/__init__.py,sha256=DU996ZsqBrEbr8Srsr8Pkm0E5-V5mJbT5xBwPpKal2s,722
layoutparser/ocr/__pycache__/__init__.cpython-310.pyc,,
layoutparser/ocr/__pycache__/base.cpython-310.pyc,,
layoutparser/ocr/__pycache__/gcv_agent.cpython-310.pyc,,
layoutparser/ocr/__pycache__/tesseract_agent.cpython-310.pyc,,
layoutparser/ocr/base.py,sha256=y876c06lRf6duIi6l6rcifmcNqUdJZJYqZYNAGrqS5E,1188
layoutparser/ocr/gcv_agent.py,sha256=BGWxEaHDEqvIYc3ZbTiNU0B_30qI3fTOUg9qgH9dS70,9896
layoutparser/ocr/tesseract_agent.py,sha256=oMwdCD6sppt_227TLLUBt4AEScrekLiAcJpTuL4VN10,6151
layoutparser/tools/__init__.py,sha256=fZT2graSF0q5K9wFyjzClx8LrMGmC0kjDtREF4L8m2o,150
layoutparser/tools/__pycache__/__init__.cpython-310.pyc,,
layoutparser/tools/__pycache__/shape_operations.cpython-310.pyc,,
layoutparser/tools/shape_operations.py,sha256=Rft4vZshuEMQnM8-lPHWbAGB5UBF66zYYCJou9zDO0g,6413
layoutparser/visualization.py,sha256=ydzusL_C4lh9Fe0X6PqKNgoqWZAeojXI_BFf2kYITdA,20456
